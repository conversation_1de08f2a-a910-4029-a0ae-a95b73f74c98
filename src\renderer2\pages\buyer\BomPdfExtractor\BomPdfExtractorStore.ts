import { create } from 'zustand';

const defaultStore   = {
    extractText: false,
    autoSelectColumns: false,
    gridOpacity: 0.7,
    currentBoxType:'description',
    pdfFile:null,
    snapToGrid:true,
    showMagnifyingGlass:true,
    overlapPercent:40,
    pdfFileName:'',
    textractRBushInitialized :false,
    doResetAll:false,
    doUndoBox:false,
    doExportCSV:false,
    doClearAll:false,
    zoomPercentage:100,
    pdfjs:null,
    allBoxes:[],
    sourceCanvasRefs:[],
    boxDrawing:0,
    s3Url:'',
    doNext:false,
    isImageBasedPdf: false,
    bomUploadID: null,
    geometryData: null,
    domesticOnly:false,
    pageRotations:0,
    scrollViewPort: null,
    updatedScrollViewPort: null,
    customBoxTypes:[],
    doResetBomPdfExtractorStore: {value:true},
    showBackToBomUploadButton: false,
    textractRBush:null,
    doAutoScroll: false,
    pdfUrl:'',
    hasBoxes:false,
}

interface BomPdfExtractorStore {
    extractText: boolean,
    autoSelectColumns: boolean,
    gridOpacity: number,
    currentBoxType: string,
    pdfFile:File|null,
    snapToGrid:boolean,
    showMagnifyingGlass:boolean,
    overlapPercent:number,
    pdfFileName:string,
    textractRBushInitialized :boolean,
    doResetAll:boolean,
    doUndoBox:boolean,
    doExportCSV:boolean,
    doClearAll:boolean,
    zoomPercentage:number,
    pdfjs:any,
    allBoxes:any,
    sourceCanvasRefs:any[],
    boxDrawing:number,
    s3Url:string,
    doNext:boolean,
    bomData:any,
    isImageBasedPdf:boolean,
    bomUploadID: string|null,
    geometryData: any,
    domesticOnly:boolean,
    pageRotations:number,
    scrollViewPort: any|null,
    updatedScrollViewPort: any|null,
    customBoxTypes:any[],
    doResetBomPdfExtractorStore: {value:boolean},
    showBackToBomUploadButton: boolean,
    textractRBush: any,
    doAutoScroll: boolean,
    pdfUrl:string,
    hasBoxes:boolean,
    productSearcher:any,
    setProductSearcher: (productSearcher:any) => void,
    setHasBoxes: (hasBoxes:boolean) => void,
    setPdfUrl: (pdfUrl:string) => void,
    setdoAutoScroll: (doAutoScroll:boolean) => void,
    setTextractRBush: (textractRBush: any) => void,
    setShowBackToBomUploadButton: (showBackToBomUploadButton:boolean) => void,
    setScrollViewPort: (scrollViewPort:any) => void,
    setUpdatedScrollViewPort: (updatedScrollViewPort:any) => void,
    setDomesticOnly: (domesticOnly:boolean) => void,
    setGeometryData: (geometryData:any) => void,
    setIsImageBasedPdf: (isImageBasedPdf:boolean) => void,
    setBomData: (bomData:any) => void,
    setPdfjs: (pdfjs:any) => void,
    setZoomPercentage: (zoomPercentage:number) => void,
    setDoResetAll: (doResetAll:boolean) => void,
    setDoUndoBox: (doUndoBox:boolean) => void,
    setDoClearAll: (doClearAll:boolean) => void,
    setPdfFileName: (pdfFileName:string) => void,
    setPdfFile: (pdfFile:File|null) => void,
    setExtractText: (extractText: boolean) => void,
    setAutoSelectColumns: (autoSelectColumns: boolean) => void,
    setGridOpacity: (gridOpacity: number) => void,
    setCurrentBoxType: (currentBoxType: string) => void,
    setTextractRBushInitialized: (textractRBushInitialized :boolean) => void,
    setDoExportCSV: (doExportCSV:boolean) => void,
    setAllBoxes: (allBoxes:any) => void,
    setSourceCanvasRefs: (sourceCanvasRefs:any[]) => void,
    setBoxDrawing: (boxDrawing:number) => void,
    setS3Url: (s3Url:string) => void,
    setDoNext: (doNext:boolean) => void,
    setBomUploadID: (bomUploadID:string|null) => void,
    resetBomPdfExtractorStore: () => void,
    setPageRotations: (pageRotations:number) => void,
    setCustomBoxTypes: (customBoxTypes:any[]) => void,
}

export const useBomPdfExtractorStore = create<BomPdfExtractorStore>((set, get) => ({
    ...defaultStore,
    bomData:null,
    setExtractText: (extractText:boolean) => set({ extractText }),
    setAutoSelectColumns: (autoSelectColumns:boolean) => set({ autoSelectColumns }),
    setGridOpacity: (gridOpacity:number) => set({ gridOpacity }),
    setCurrentBoxType: (currentBoxType:string) => set({ currentBoxType }),
    setPdfFile: (pdfFile:File|null) => set({ pdfFile }),
    setSnapToGrid: (snapToGrid:boolean) => set({ snapToGrid }),
    setShowMagnifyingGlass: (showMagnifyingGlass:boolean) => set({ showMagnifyingGlass }),
    setOverlapPercent: (overlapPercent:number) => set({ overlapPercent }),
    setPdfFileName: (pdfFileName:string) => set({ pdfFileName }),
    setTextractRBushInitialized: (textractRBushInitialized :boolean) => set({textractRBushInitialized}),
    setDoResetAll: (doResetAll:boolean) => set({doResetAll}),
    setDoUndoBox: (doUndoBox:boolean) => set({doUndoBox}),
    setDoExportCSV: (doExportCSV:boolean) => set({doExportCSV}),
    setDoClearAll: (doClearAll:boolean) => set({doClearAll}),
    setZoomPercentage: (zoomPercentage:number) => set({zoomPercentage}),
    setPdfjs: (pdfjs:any) => set({pdfjs}),
    setAllBoxes: (allBoxes:any) => set({allBoxes}),
    setSourceCanvasRefs: (sourceCanvasRefs:any[]) => set({sourceCanvasRefs}),
    setBoxDrawing: (boxDrawing:number) => set({boxDrawing}),
    setS3Url: (s3Url:string) => set({s3Url}),
    setDoNext: (doNext:boolean) => set({doNext}),
    setBomData: (bomData:any) => set({bomData}),
    setIsImageBasedPdf: (isImageBasedPdf:boolean) => set({isImageBasedPdf}),
    setBomUploadID: (bomUploadID:string|null) => set({bomUploadID}),
    setGeometryData: (geometryData:any) => set({geometryData}),
    setDomesticOnly: (domesticOnly:boolean) => set({domesticOnly}),
    setPageRotations: (pageRotations:number) => set({pageRotations}),
    setScrollViewPort: (scrollViewPort:any) => set({scrollViewPort}),
    setCustomBoxTypes: (customBoxTypes:any[]) => set({customBoxTypes}),
    setUpdatedScrollViewPort: (updatedScrollViewPort:any) => set({updatedScrollViewPort}),
    setShowBackToBomUploadButton: (showBackToBomUploadButton:boolean) => set({showBackToBomUploadButton}),
    setTextractRBush: (textractRBush: any) => set({textractRBush}),
    setdoAutoScroll: (doAutoScroll:boolean) => set({doAutoScroll}),
    setPdfUrl: (pdfUrl:string) => set({pdfUrl}),
    setHasBoxes: (hasBoxes:boolean) => set({hasBoxes}),
    setProductSearcher: (productSearcher:any) => set({productSearcher}),
    productSearcher: null,//This needs to persist do not reset it. 
    resetBomPdfExtractorStore: () => set(state => ({
        ...defaultStore, 
        allBoxes:[],
        sourceCanvasRefs:[],
        scrollViewPort:null,   
        updatedScrollViewPort:null,
        customBoxTypes:[],
        doResetBomPdfExtractorStore: {value:true},
        textractRBush:null,
    })),
}));