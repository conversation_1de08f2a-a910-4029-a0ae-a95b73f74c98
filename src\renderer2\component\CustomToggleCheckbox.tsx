import React from 'react';
import { Control, Controller, FieldValues, Path } from 'react-hook-form';
import styles from './CustomToggleCheckbox.module.scss';

interface CustomToggleCheckboxProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  defaultValue?: boolean;
  onChange?: (value: boolean) => void;
}

const CustomToggleCheckbox = <T extends FieldValues>({
  name,
  control,
  label,
  defaultValue = false,
  onChange,
  onKeyDown
}: CustomToggleCheckboxProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={defaultValue as any}
      render={({ field: { onChange: fieldOnChange, value } }) => (
        <div className={styles.toggleContainer}>
          <div 
            className={`${styles.toggle} ${value ? styles.active : ''}`}
            tabIndex={0}
            onClick={() => {
              const newValue = !value;
              fieldOnChange(newValue);
              if (onChange) onChange(newValue);
            }}
            onKeyDown={(e) => {
              if(e.key === 'Enter'){
                const newValue = !value;
                fieldOnChange(newValue);
                if (onChange) onChange(newValue);
              }
              if(onKeyDown) onKeyDown(e);
            }}
          >
            <div className={styles.toggleSwitch}></div>
            <span className={styles.toggleText}>{value ? 'YES' : 'NO'}</span>
          </div>
          {label && <label className={styles.toggleLabel}>{label}</label>}
        </div>
      )}
    />
  );
};

export default CustomToggleCheckbox; 